#!/bin/bash
# Exemple de configuration multi-serveurs pour FiveM XDP Filter
# Ce script déploie plusieurs serveurs FiveM avec protection XDP

set -e

# Configuration des serveurs
declare -A SERVERS=(
    ["main-server"]="*************:large:eth0"
    ["test-server"]="*************:medium:eth0"
    ["dev-server"]="*************:small:eth0"
    ["backup-server"]="*************:medium:eth1"
)

# Couleurs pour les logs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅${NC} $1"
}

# Vérifier les prérequis
if [ "$(id -u)" != "0" ]; then
    echo "❌ Ce script doit être exécuté avec des privilèges root"
    exit 1
fi

if [ ! -f "../deploy.sh" ]; then
    echo "❌ Script de déploiement non trouvé"
    echo "Assurez-vous d'être dans le répertoire config/examples/"
    exit 1
fi

log "Déploiement de la configuration multi-serveurs FiveM XDP"
log "Nombre de serveurs à déployer: ${#SERVERS[@]}"

# Déployer d'abord la stack de surveillance
log "Déploiement de la stack de surveillance..."
cd ../..
sudo ./deploy.sh monitoring
cd config/examples

# Attendre que la surveillance soit prête
sleep 15

# Déployer chaque serveur
for server_name in "${!SERVERS[@]}"; do
    IFS=':' read -r server_ip server_size interface <<< "${SERVERS[$server_name]}"
    
    log "Déploiement du serveur: $server_name"
    log "  - IP: $server_ip"
    log "  - Taille: $server_size"
    log "  - Interface: $interface"
    
    cd ../..
    sudo ./deploy.sh deploy \
        -s "$server_ip" \
        -n "$server_name" \
        -z "$server_size" \
        -i "$interface" \
        --no-monitoring
    cd config/examples
    
    # Attendre entre les déploiements
    sleep 5
done

log_success "Déploiement multi-serveurs terminé!"
log "Accès aux interfaces:"
log "  - Grafana: http://localhost:3000 (admin/admin123)"
log "  - Prometheus: http://localhost:9090"
log "  - AlertManager: http://localhost:9093"

# Afficher l'état des services
log "État des services déployés:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep fivem

log_success "Configuration multi-serveurs prête!"
log "Consultez Grafana pour voir les métriques de tous vos serveurs."
