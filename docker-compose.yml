# Docker Compose pour le déploiement complet FiveM XDP Filter
# Inclut la surveillance, les métriques et la gestion centralisée

version: '3.8'

services:
  # Stack de surveillance complète
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: fivem-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/monitoring/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - fivem-monitoring

  grafana:
    image: grafana/grafana:10.1.0
    container_name: fivem-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH=/var/lib/grafana/dashboards/fivem-xdp-overview.json
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - fivem-monitoring
    depends_on:
      - prometheus

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: fivem-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./docker/monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - fivem-monitoring

  # Métriques système de l'hôte
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: fivem-node-exporter
    restart: unless-stopped
    ports:
      - "9101:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - fivem-monitoring

  # Métriques des conteneurs
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: fivem-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - fivem-monitoring

  # Service de gestion centralisée (optionnel)
  fivem-manager:
    build:
      context: .
      dockerfile: docker/manager/Dockerfile
    container_name: fivem-manager
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MANAGER_PORT=8000
      - PROMETHEUS_URL=http://prometheus:9090
      - GRAFANA_URL=http://grafana:3000
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config:/app/config:ro
    networks:
      - fivem-monitoring
    depends_on:
      - prometheus
      - grafana

  # Webhook pour les alertes (optionnel)
  webhook-receiver:
    image: nginx:alpine
    container_name: fivem-webhook
    restart: unless-stopped
    ports:
      - "5001:80"
    volumes:
      - ./docker/webhook/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/webhook/html:/usr/share/nginx/html:ro
    networks:
      - fivem-monitoring

networks:
  fivem-monitoring:
    driver: bridge
    name: fivem-monitoring

volumes:
  prometheus_data:
    name: fivem-prometheus-data
  grafana_data:
    name: fivem-grafana-data
  alertmanager_data:
    name: fivem-alertmanager-data
