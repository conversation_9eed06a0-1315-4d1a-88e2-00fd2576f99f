{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(fivem_xdp_packets_total[1m])", "interval": "", "legendFormat": "{{server_name}} - {{action}}", "refId": "A"}], "title": "<PERSON>x de Paquets par Serveur", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "fivem_xdp_filter_active", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "État des Filtres", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 5000}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "fivem_xdp_avg_processing_time_ns", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Temps de Traitement Moyen", "type": "stat"}], "refresh": "5s", "schemaVersion": 38, "style": "dark", "tags": ["fivem", "xdp", "security"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "FiveM XDP Filter - Vue d'ensemble", "uid": "fivem-xdp-overview", "version": 1, "weekStart": ""}