#!/bin/bash
# Script de déploiement automatisé pour les filtres XDP FiveM
# Déploie et configure automatiquement les filtres XDP avec surveillance

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration par défaut
DEFAULT_INTERFACE="eth0"
DEFAULT_SERVER_SIZE="medium"
DEFAULT_MONITORING_PORT="3000"
DEFAULT_PROMETHEUS_PORT="9090"

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌${NC} $1"
}

log_info() {
    echo -e "${PURPLE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️${NC} $1"
}

# Afficher le banner
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    FiveM XDP Filter                          ║"
    echo "║              Déploiement Automatisé v1.0                    ║"
    echo "║                                                              ║"
    echo "║  🛡️  Protection DDoS avancée pour serveurs FiveM            ║"
    echo "║  📊  Surveillance en temps réel avec Grafana                ║"
    echo "║  🐳  Déploiement containerisé                               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Afficher l'aide
show_help() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "COMMANDES:"
    echo "  deploy          Déployer un nouveau serveur avec filtre XDP"
    echo "  monitoring      Déployer uniquement la stack de surveillance"
    echo "  remove          Supprimer un serveur et son filtre"
    echo "  list            Lister les serveurs déployés"
    echo "  status          Afficher l'état des services"
    echo "  logs            Afficher les logs des services"
    echo "  update          Mettre à jour les filtres existants"
    echo ""
    echo "OPTIONS:"
    echo "  -s, --server-ip IP        Adresse IP du serveur FiveM (requis pour deploy)"
    echo "  -i, --interface IFACE     Interface réseau (défaut: $DEFAULT_INTERFACE)"
    echo "  -z, --size SIZE           Taille du serveur: small|medium|large (défaut: $DEFAULT_SERVER_SIZE)"
    echo "  -n, --name NAME           Nom du serveur (défaut: auto-généré)"
    echo "  -p, --port PORT           Port du serveur FiveM (défaut: 30120)"
    echo "  -m, --monitoring-port     Port Grafana (défaut: $DEFAULT_MONITORING_PORT)"
    echo "  --prometheus-port         Port Prometheus (défaut: $DEFAULT_PROMETHEUS_PORT)"
    echo "  --no-monitoring           Ne pas déployer la surveillance"
    echo "  --force                   Forcer le déploiement (écraser existant)"
    echo "  -h, --help                Afficher cette aide"
    echo ""
    echo "EXEMPLES:"
    echo "  $0 deploy -s ************* -n main-server"
    echo "  $0 deploy -s ********* -z large -i ens3"
    echo "  $0 monitoring"
    echo "  $0 status"
    echo "  $0 remove -n main-server"
}

# Vérifier les prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    # Vérifier les privilèges root
    if [ "$(id -u)" != "0" ]; then
        log_error "Ce script doit être exécuté avec des privilèges root"
        log_error "Utilisez: sudo $0"
        exit 1
    fi
    
    # Vérifier Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker n'est pas installé"
        log_error "Installez Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose n'est pas installé"
        log_error "Installez Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Vérifier les outils BPF
    if ! command -v bpftool >/dev/null 2>&1; then
        log_warning "bpftool n'est pas installé, installation..."
        apt-get update && apt-get install -y linux-tools-common linux-tools-generic bpftool
    fi
    
    # Vérifier le support XDP du kernel
    if [ ! -d "/sys/fs/bpf" ]; then
        log_error "Le système de fichiers BPF n'est pas monté"
        log_error "Montez-le avec: mount -t bpf bpf /sys/fs/bpf"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Créer la configuration pour un serveur
create_server_config() {
    local server_name="$1"
    local server_ip="$2"
    local server_port="$3"
    local server_size="$4"
    local interface="$5"
    
    local config_dir="config/servers/$server_name"
    mkdir -p "$config_dir"
    
    cat > "$config_dir/server.env" <<EOF
# Configuration pour le serveur $server_name
SERVER_NAME=$server_name
SERVER_IP=$server_ip
SERVER_PORT=$server_port
SERVER_SIZE=$server_size
XDP_INTERFACE=$interface
AUTO_DEPLOY=true
METRICS_ENABLED=true
EXPORTER_PORT=9100
CREATED_AT=$(date -Iseconds)
EOF
    
    log_success "Configuration créée pour $server_name"
}

# Déployer un serveur
deploy_server() {
    local server_name="$1"
    local server_ip="$2"
    local server_port="$3"
    local server_size="$4"
    local interface="$5"
    local force="$6"
    
    log "Déploiement du serveur $server_name..."
    
    # Vérifier si le serveur existe déjà
    if [ -f "config/servers/$server_name/server.env" ] && [ "$force" != "true" ]; then
        log_error "Le serveur $server_name existe déjà"
        log_error "Utilisez --force pour écraser ou choisissez un autre nom"
        exit 1
    fi
    
    # Créer la configuration
    create_server_config "$server_name" "$server_ip" "$server_port" "$server_size" "$interface"
    
    # Construire l'image du gestionnaire XDP
    log "Construction de l'image du gestionnaire XDP..."
    docker build -t fivem-xdp-manager:latest docker/xdp-manager/
    
    # Construire l'image de l'exportateur de métriques
    log "Construction de l'image de l'exportateur de métriques..."
    docker build -t fivem-metrics-exporter:latest docker/metrics-exporter/
    
    # Déployer le gestionnaire XDP
    log "Déploiement du gestionnaire XDP pour $server_name..."
    docker run -d \
        --name "fivem-xdp-$server_name" \
        --privileged \
        --network host \
        --pid host \
        --env-file "config/servers/$server_name/server.env" \
        -v /sys/fs/bpf:/sys/fs/bpf:shared \
        -v /proc:/host/proc:ro \
        -v /sys:/host/sys:ro \
        --restart unless-stopped \
        fivem-xdp-manager:latest
    
    # Déployer l'exportateur de métriques
    log "Déploiement de l'exportateur de métriques pour $server_name..."
    docker run -d \
        --name "fivem-metrics-$server_name" \
        --network host \
        --pid host \
        --env-file "config/servers/$server_name/server.env" \
        -v /sys/fs/bpf:/sys/fs/bpf:ro \
        --restart unless-stopped \
        fivem-metrics-exporter:latest
    
    # Attendre que les services démarrent
    sleep 5
    
    # Vérifier le déploiement
    if docker ps | grep -q "fivem-xdp-$server_name" && docker ps | grep -q "fivem-metrics-$server_name"; then
        log_success "Serveur $server_name déployé avec succès"
        log_info "Filtre XDP: Interface $interface, IP $server_ip:$server_port"
        log_info "Métriques disponibles sur: http://localhost:9100/metrics"
    else
        log_error "Échec du déploiement du serveur $server_name"
        exit 1
    fi
}

# Déployer la stack de surveillance
deploy_monitoring() {
    log "Déploiement de la stack de surveillance..."
    
    cd docker/monitoring
    
    # Créer le fichier d'environnement pour Grafana
    cat > .env <<EOF
GRAFANA_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
EOF
    
    # Déployer avec Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    cd ../..
    
    # Attendre que les services démarrent
    sleep 10
    
    # Vérifier le déploiement
    if curl -s http://localhost:3000 >/dev/null && curl -s http://localhost:9090 >/dev/null; then
        log_success "Stack de surveillance déployée avec succès"
        log_info "Grafana: http://localhost:3000 (admin/admin123)"
        log_info "Prometheus: http://localhost:9090"
        log_info "AlertManager: http://localhost:9093"
    else
        log_error "Échec du déploiement de la surveillance"
        exit 1
    fi
}

# Variables par défaut
SERVER_IP=""
INTERFACE="$DEFAULT_INTERFACE"
SERVER_SIZE="$DEFAULT_SERVER_SIZE"
SERVER_NAME=""
SERVER_PORT="30120"
MONITORING_PORT="$DEFAULT_MONITORING_PORT"
PROMETHEUS_PORT="$DEFAULT_PROMETHEUS_PORT"
NO_MONITORING="false"
FORCE="false"
COMMAND=""

# Parser les arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--server-ip)
            SERVER_IP="$2"
            shift 2
            ;;
        -i|--interface)
            INTERFACE="$2"
            shift 2
            ;;
        -z|--size)
            SERVER_SIZE="$2"
            shift 2
            ;;
        -n|--name)
            SERVER_NAME="$2"
            shift 2
            ;;
        -p|--port)
            SERVER_PORT="$2"
            shift 2
            ;;
        -m|--monitoring-port)
            MONITORING_PORT="$2"
            shift 2
            ;;
        --prometheus-port)
            PROMETHEUS_PORT="$2"
            shift 2
            ;;
        --no-monitoring)
            NO_MONITORING="true"
            shift
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        deploy|monitoring|remove|list|status|logs|update)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Fonction principale
main() {
    show_banner
    
    if [ -z "$COMMAND" ]; then
        log_error "Aucune commande spécifiée"
        show_help
        exit 1
    fi
    
    check_prerequisites
    
    case "$COMMAND" in
        "deploy")
            if [ -z "$SERVER_IP" ]; then
                log_error "L'adresse IP du serveur est requise pour le déploiement"
                log_error "Utilisez: $0 deploy -s <IP_SERVEUR>"
                exit 1
            fi
            
            # Générer un nom de serveur si non spécifié
            if [ -z "$SERVER_NAME" ]; then
                SERVER_NAME="server-$(echo $SERVER_IP | tr '.' '-')"
            fi
            
            deploy_server "$SERVER_NAME" "$SERVER_IP" "$SERVER_PORT" "$SERVER_SIZE" "$INTERFACE" "$FORCE"
            
            if [ "$NO_MONITORING" != "true" ]; then
                deploy_monitoring
            fi
            ;;
        "monitoring")
            deploy_monitoring
            ;;
        *)
            log_error "Commande non implémentée: $COMMAND"
            exit 1
            ;;
    esac
    
    log_success "Déploiement terminé avec succès!"
}

# Exécuter la fonction principale
main "$@"
